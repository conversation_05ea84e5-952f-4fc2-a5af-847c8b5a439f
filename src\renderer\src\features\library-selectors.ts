import { createSelector } from '@reduxjs/toolkit';
import type { RootState } from '@renderer/store';
import type { LibraryGame, GameCollection } from '@types';

// Base selectors
const selectLibraryState = (state: RootState) => state.library;
const selectLibraryCollectionsState = (state: RootState) => state.libraryCollections;

// Memoized library selectors
export const selectLibrary = createSelector(
  [selectLibraryState],
  (libraryState) => libraryState.value
);

export const selectCollections = createSelector(
  [selectLibraryCollectionsState],
  (collectionsState) => collectionsState.collections
);

export const selectLibraryFilters = createSelector(
  [selectLibraryCollectionsState],
  (collectionsState) => collectionsState.filters
);

export const selectSelectedCollection = createSelector(
  [selectLibraryCollectionsState],
  (collectionsState) => collectionsState.selectedCollection
);

export const selectViewMode = createSelector(
  [selectLibraryCollectionsState],
  (collectionsState) => collectionsState.viewMode
);

export const selectCardSize = createSelector(
  [selectLibraryCollectionsState],
  (collectionsState) => collectionsState.cardSize
);

// Memoized collections map for O(1) lookups
export const selectCollectionsMap = createSelector(
  [selectCollections],
  (collections) => {
    const map = new Map<string, GameCollection>();
    collections.forEach(collection => {
      map.set(collection.id, collection);
    });
    return map;
  }
);

// Memoized game sets for each collection for faster lookups
export const selectCollectionGameSets = createSelector(
  [selectCollections],
  (collections) => {
    const sets = new Map<string, Set<string>>();
    collections.forEach(collection => {
      sets.set(collection.id, new Set(collection.gameIds));
    });
    return sets;
  }
);

// Memoized filtered games based on current collection
export const selectFilteredGamesByCollection = createSelector(
  [selectLibrary, selectSelectedCollection, selectCollectionGameSets],
  (library, selectedCollection, collectionGameSets) => {
    if (!selectedCollection) return library;
    
    const gameSet = collectionGameSets.get(selectedCollection);
    if (!gameSet) return [];
    
    return library.filter(game => gameSet.has(game.id));
  }
);

// Memoized smart collections
export const selectSmartCollections = createSelector(
  [selectLibrary],
  (library) => {
    const now = Date.now();
    const recentlyPlayedThreshold = 7 * 24 * 60 * 60 * 1000; // 7 days
    
    return {
      'recently-played': library.filter(game => 
        game.lastTimePlayed && (now - new Date(game.lastTimePlayed).getTime()) < recentlyPlayedThreshold
      ),
      'favorites': library.filter(game => game.favorite),
      'installed': library.filter(game => Boolean(game.executablePath)),
      'not-played': library.filter(game => !game.playTimeInMilliseconds || game.playTimeInMilliseconds === 0),
    };
  }
);

// Memoized collection stats
export const selectCollectionStats = createSelector(
  [selectCollections, selectLibrary, selectCollectionGameSets],
  (collections, library, collectionGameSets) => {
    const stats = new Map<string, { gameCount: number; totalPlayTime: number; lastPlayed: Date | null }>();
    
    collections.forEach(collection => {
      const gameSet = collectionGameSets.get(collection.id);
      if (!gameSet) {
        stats.set(collection.id, { gameCount: 0, totalPlayTime: 0, lastPlayed: null });
        return;
      }
      
      const gamesInCollection = library.filter(game => gameSet.has(game.id));
      
      let totalPlayTime = 0;
      let lastPlayed: Date | null = null;
      
      for (const game of gamesInCollection) {
        totalPlayTime += game.playTimeInMilliseconds || 0;
        
        if (game.lastTimePlayed) {
          const gameDate = new Date(game.lastTimePlayed);
          if (!lastPlayed || gameDate > lastPlayed) {
            lastPlayed = gameDate;
          }
        }
      }
      
      stats.set(collection.id, {
        gameCount: gamesInCollection.length,
        totalPlayTime,
        lastPlayed,
      });
    });
    
    return stats;
  }
);

// Memoized search results
export const selectSearchResults = createSelector(
  [selectLibrary, selectLibraryFilters],
  (library, filters) => {
    if (!filters.searchQuery || filters.searchQuery.length < 2) {
      return library;
    }
    
    const query = filters.searchQuery.toLowerCase();
    return library.filter(game => 
      game.title.toLowerCase().includes(query)
    );
  }
);

// Memoized genre map for better performance
export const selectGameGenresMap = createSelector(
  [selectLibrary],
  (library) => {
    const genresMap = new Map<string, string[]>();
    
    library.forEach(game => {
      if (game.genres && game.genres.length > 0) {
        genresMap.set(game.id, game.genres);
      }
    });
    
    return genresMap;
  }
);

// Memoized unique genres list
export const selectUniqueGenres = createSelector(
  [selectGameGenresMap],
  (genresMap) => {
    const allGenres = new Set<string>();
    
    genresMap.forEach(genres => {
      genres.forEach(genre => allGenres.add(genre));
    });
    
    return Array.from(allGenres).sort();
  }
);
