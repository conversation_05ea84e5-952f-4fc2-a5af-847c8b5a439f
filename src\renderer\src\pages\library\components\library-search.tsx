import React, { useState, useRef, useEffect, useMemo, useCallback, memo } from "react";
import { useTranslation } from "react-i18next";
import { SearchIcon, XIcon } from "@primer/octicons-react";

import type { LibraryGame } from "@types";

import "./library-search.scss";

interface LibrarySearchProps {
  value: string;
  onChange: (value: string) => void;
  games: LibraryGame[];
  placeholder?: string;
  className?: string;
}

// Custom hook for debounced search
function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

export const LibrarySearch = memo<LibrarySearchProps>(({
  value,
  onChange,
  games,
  placeholder,
  className
}) => {
  const { t } = useTranslation("library");
  const [isFocused, setIsFocused] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [localValue, setLocalValue] = useState(value);
  const inputRef = useRef<HTMLInputElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);

  // Debounce the search to improve performance
  const debouncedValue = useDebounce(localValue, 300);

  // Update parent when debounced value changes
  useEffect(() => {
    onChange(debouncedValue);
  }, [debouncedValue, onChange]);

  // Sync local value with prop value when it changes externally
  useEffect(() => {
    if (value !== localValue) {
      setLocalValue(value);
    }
  }, [value]);

  // Memoized search suggestions with better performance
  const suggestions = useMemo(() => {
    if (!localValue || localValue.length < 2) return [];

    const query = localValue.toLowerCase();
    const matchingGames = games
      .filter(game =>
        game.title.toLowerCase().includes(query) &&
        game.title.toLowerCase() !== query
      )
      .slice(0, 5) // Limit to 5 suggestions
      .map(game => game.title);

    // Remove duplicates and sort by relevance
    return Array.from(new Set(matchingGames)).sort((a, b) => {
      const aIndex = a.toLowerCase().indexOf(query);
      const bIndex = b.toLowerCase().indexOf(query);
      
      // Prioritize matches at the beginning of the title
      if (aIndex !== bIndex) {
        return aIndex - bIndex;
      }
      
      // Then sort alphabetically
      return a.localeCompare(b);
    });
  }, [value, games]);

  // Close suggestions when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        suggestionsRef.current && 
        !suggestionsRef.current.contains(event.target as Node) &&
        inputRef.current &&
        !inputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    }

    if (showSuggestions) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => document.removeEventListener("mousedown", handleClickOutside);
    }
  }, [showSuggestions]);

  // Handle keyboard navigation
  useEffect(() => {
    function handleKeyDown(event: KeyboardEvent) {
      if (event.key === "Escape") {
        setShowSuggestions(false);
        inputRef.current?.blur();
      }
    }

    if (showSuggestions) {
      document.addEventListener("keydown", handleKeyDown);
      return () => document.removeEventListener("keydown", handleKeyDown);
    }
  }, [showSuggestions]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setLocalValue(newValue);

    // Show suggestions if there's input and we have suggestions
    setShowSuggestions(newValue.length >= 2);
  }, []);

  const handleInputFocus = () => {
    setIsFocused(true);
    if (value.length >= 2 && suggestions.length > 0) {
      setShowSuggestions(true);
    }
  };

  const handleInputBlur = () => {
    setIsFocused(false);
    // Delay hiding suggestions to allow for clicks
    setTimeout(() => setShowSuggestions(false), 150);
  };

  const handleSuggestionClick = (suggestion: string) => {
    onChange(suggestion);
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  const handleClearSearch = () => {
    onChange("");
    setShowSuggestions(false);
    inputRef.current?.focus();
  };

  const hasValue = value.length > 0;

  return (
    <div className={`library-search ${className || ""}`}>
      <div className={`library-search__input-container ${isFocused ? "library-search__input-container--focused" : ""}`}>
        <div className="library-search__input-wrapper">
          <SearchIcon size={16} className="library-search__search-icon" />
          
          <input
            ref={inputRef}
            type="text"
            placeholder={placeholder || t("search_library")}
            value={localValue}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onBlur={handleInputBlur}
            className="library-search__input"
          />
          
          {hasValue && (
            <button
              type="button"
              className="library-search__clear-button"
              onClick={handleClearSearch}
              title={t("clear_search")}
            >
              <XIcon size={14} />
            </button>
          )}
        </div>

        {/* Search Suggestions */}
        {showSuggestions && suggestions.length > 0 && (
          <div className="library-search__suggestions" ref={suggestionsRef}>
            <div className="library-search__suggestions-header">
              <span className="library-search__suggestions-title">{t("suggestions")}</span>
            </div>
            
            <div className="library-search__suggestions-list">
              {suggestions.map((suggestion, index) => (
                <button
                  key={index}
                  type="button"
                  className="library-search__suggestion"
                  onClick={() => handleSuggestionClick(suggestion)}
                >
                  <SearchIcon size={14} className="library-search__suggestion-icon" />
                  <span className="library-search__suggestion-text">{suggestion}</span>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Search Stats */}
      {hasValue && (
        <div className="library-search__stats">
          <span className="library-search__stats-text">
            {games.filter(game =>
              game.title.toLowerCase().includes(localValue.toLowerCase())
            ).length} {t("results_found")}
          </span>
        </div>
      )}
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison for better performance
  return (
    prevProps.value === nextProps.value &&
    prevProps.games.length === nextProps.games.length &&
    prevProps.placeholder === nextProps.placeholder &&
    prevProps.className === nextProps.className
  );
});

LibrarySearch.displayName = 'LibrarySearch';
