import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";

import type { GameCollection, LibraryViewMode, LibraryFilters, LibrarySortBy } from "@types";

export type LibraryCardSize = "compact" | "normal" | "large";

export interface LibraryCollectionsState {
  collections: GameCollection[];
  viewMode: LibraryViewMode;
  cardSize: LibraryCardSize;
  filters: LibraryFilters;
  selectedCollection: string | null;
  collectionSource: "insights" | "navigation" | null; // Track how collection was accessed
}

// Load saved sort preference from localStorage with validation
const savedSortByValue = localStorage.getItem("librarySortBy");
const validSortOptions: LibrarySortBy[] = [
  "name-asc", "name-desc", "last-played-asc", "last-played-desc",
  "playtime-asc", "playtime-desc", "status-asc", "status-desc"
];
const savedSortBy = (savedSortByValue && validSortOptions.includes(savedSortByValue as LibrarySortBy))
  ? savedSortByValue as LibrarySortBy
  : "name-asc";

const initialState: LibraryCollectionsState = {
  collections: [],
  viewMode: "grid",
  cardSize: "normal",
  filters: {
    genres: [],
    collections: [],
    searchQuery: "",
    showInstalledOnly: false,
    showNotInstalledOnly: false,
    sortBy: savedSortBy,
  },
  selectedCollection: null,
  collectionSource: null,
};

export const libraryCollectionsSlice = createSlice({
  name: "libraryCollections",
  initialState,
  reducers: {
    setCollections: (state, action: PayloadAction<GameCollection[]>) => {
      state.collections = action.payload;
    },
    addCollection: (state, action: PayloadAction<GameCollection>) => {
      state.collections.push(action.payload);
    },
    updateCollection: (state, action: PayloadAction<GameCollection>) => {
      const index = state.collections.findIndex(
        (collection) => collection.id === action.payload.id
      );
      if (index !== -1) {
        state.collections[index] = action.payload;
      }
    },
    removeCollection: (state, action: PayloadAction<string>) => {
      state.collections = state.collections.filter(
        (collection) => collection.id !== action.payload
      );
    },
    addGameToCollection: (
      state,
      action: PayloadAction<{ collectionId: string; gameId: string }>
    ) => {
      const collection = state.collections.find(
        (c) => c.id === action.payload.collectionId
      );
      if (collection && !collection.gameIds.includes(action.payload.gameId)) {
        collection.gameIds.push(action.payload.gameId);
        collection.updatedAt = new Date();
      }
    },
    removeGameFromCollection: (
      state,
      action: PayloadAction<{ collectionId: string; gameId: string }>
    ) => {
      const collection = state.collections.find(
        (c) => c.id === action.payload.collectionId
      );
      if (collection) {
        collection.gameIds = collection.gameIds.filter(
          (id) => id !== action.payload.gameId
        );
        collection.updatedAt = new Date();
      }
    },
    setViewMode: (state, action: PayloadAction<LibraryViewMode>) => {
      state.viewMode = action.payload;
    },
    setCardSize: (state, action: PayloadAction<LibraryCardSize>) => {
      state.cardSize = action.payload;
    },
    setLibraryFilters: (state, action: PayloadAction<Partial<LibraryFilters>>) => {
      state.filters = { ...state.filters, ...action.payload };

      // Save sort preference to localStorage when sortBy changes
      if (action.payload.sortBy) {
        localStorage.setItem("librarySortBy", action.payload.sortBy);
      }
    },
    clearLibraryFilters: (state) => {
      state.filters = initialState.filters;
    },
    setSelectedCollection: (state, action: PayloadAction<string | null>) => {
      state.selectedCollection = action.payload;
    },
    setCollectionSource: (state, action: PayloadAction<"insights" | "navigation" | null>) => {
      state.collectionSource = action.payload;
    },
  },
});

export const {
  setCollections,
  addCollection,
  updateCollection,
  removeCollection,
  addGameToCollection,
  removeGameFromCollection,
  setViewMode,
  setCardSize,
  setLibraryFilters,
  clearLibraryFilters,
  setSelectedCollection,
  setCollectionSource,
} = libraryCollectionsSlice.actions;
