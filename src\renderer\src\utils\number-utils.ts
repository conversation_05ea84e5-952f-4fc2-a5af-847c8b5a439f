/**
 * Utility functions for number formatting and display
 */

/**
 * Formats large numbers with abbreviations for better UI display
 * @param value - The number to format
 * @param precision - Number of decimal places for abbreviated values (default: 1)
 * @returns Formatted string with abbreviations (e.g., "1.2k", "3.4M")
 */
export function formatLargeNumber(value: number, precision: number = 1): string {
  if (value < 1000) {
    return value.toString();
  }
  
  if (value < 1000000) {
    const abbreviated = value / 1000;
    return abbreviated % 1 === 0 
      ? `${Math.floor(abbreviated)}k`
      : `${abbreviated.toFixed(precision)}k`;
  }
  
  if (value < 1000000000) {
    const abbreviated = value / 1000000;
    return abbreviated % 1 === 0 
      ? `${Math.floor(abbreviated)}M`
      : `${abbreviated.toFixed(precision)}M`;
  }
  
  const abbreviated = value / 1000000000;
  return abbreviated % 1 === 0 
    ? `${Math.floor(abbreviated)}B`
    : `${abbreviated.toFixed(precision)}B`;
}

/**
 * Formats playtime in milliseconds to a readable format with abbreviations for large values
 * @param milliseconds - Playtime in milliseconds
 * @param useAbbreviations - Whether to use abbreviations for large hour values (default: true)
 * @returns Formatted playtime string
 */
export function formatPlayTime(milliseconds: number, useAbbreviations: boolean = true): string {
  if (!milliseconds || milliseconds === 0) return "0m";

  const hours = Math.floor(milliseconds / (1000 * 60 * 60));
  const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));

  if (hours === 0) {
    return `${minutes}m`;
  }

  // For large hour values, use abbreviations if enabled
  if (useAbbreviations && hours >= 1000) {
    return `${formatLargeNumber(hours)}h`;
  }

  // For smaller values or when abbreviations are disabled, show full format
  if (minutes === 0) {
    return `${hours}h`;
  }
  
  return `${hours}h ${minutes}m`;
}

/**
 * Determines if a number should be displayed with abbreviations based on UI constraints
 * @param value - The number to check
 * @param threshold - The threshold above which abbreviations should be used (default: 1000)
 * @returns Whether abbreviations should be used
 */
export function shouldUseAbbreviation(value: number, threshold: number = 1000): boolean {
  return value >= threshold;
}
