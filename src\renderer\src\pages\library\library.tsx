import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import {
  AppsIcon,
  DownloadIcon,
  GearIcon,
  PlusIcon,
  SearchIcon,
  XIcon,
  PlayIcon,
  CheckIcon,
  SquareIcon,
  SquareFillIcon,
  StopIcon,
  ListUnorderedIcon,
  PencilIcon,
  TrashIcon,
  FileDirectoryIcon,
  ChevronDownIcon,
  ChevronRightIcon,
  FilterIcon,
  SortAscIcon,
  SortDescIcon,
  ThreeBarsIcon,
  SidebarExpandIcon,
  SidebarCollapseIcon,
} from "@primer/octicons-react";

import { Button, TextField } from "@renderer/components";
import { useLibrary, useLibraryCollections } from "@renderer/hooks";
import { filterGamesByGenres } from "@renderer/utils/genre-utils";
import { sortLibraryGames } from "@renderer/utils/library-sort-utils";
import { wasPlayedRecently } from "@renderer/utils/date-utils";
import { useSearchParams } from "react-router-dom";

import { LibraryGameGrid } from "./components/library-game-grid";
import { LibraryGameList } from "./components/library-game-list";

import { LibrarySortDropdown } from "./components/library-sort-dropdown";
import { LibrarySearch } from "./components/library-search";
import { LibraryFilterChips } from "./components/library-filter-chips";
import { LibraryNews } from "./components/library-news";
import { LibraryCollectionHeader } from "./components/library-collection-header";
import { CollectionModal } from "./components/collection-modal";
import { CollectionSelectorModal } from "./components/collection-selector-modal";
import { DeleteCollectionModal } from "./components/delete-collection-modal";
import { CollectionsTabs } from "./components/collections-tabs";
import { LibraryLoading } from "./components/library-loading";
import { LibraryEmptyState } from "./components/library-empty-state";
import { LibrarySkeleton } from "./components/library-skeleton";

// New enhanced components
import { LibraryNavigationBar } from "./components/library-navigation-bar";
import { LibraryCollectionsSidebar } from "./components/library-collections-sidebar";
import { LibraryQuickFilters } from "./components/library-quick-filters";

import type { LibraryGame, GameCollection } from "@types";

import "./library.scss";

export default function Library() {
  const { t } = useTranslation("library");
  const { library } = useLibrary();
  const [searchParams, setSearchParams] = useSearchParams();
  const {
    viewMode,
    cardSize,
    filters,
    selectedCollection,
    collectionSource,
    collections,
    changeViewMode,
    changeCardSize,
    updateFilters,
    updateSortBy,
    resetFilters,
    selectCollection,
    loadCollections,
    removeGameFromCollectionById,
  } = useLibraryCollections();


  const [showCollectionsSidebar, setShowCollectionsSidebar] = useState(false);
  const [showCollectionModal, setShowCollectionModal] = useState(false);
  const [showCollectionSelector, setShowCollectionSelector] = useState(false);
  const [selectedGameForCollection, setSelectedGameForCollection] = useState<LibraryGame | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingGames, setIsLoadingGames] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Handle URL parameters for collection navigation
  useEffect(() => {
    const collectionParam = searchParams.get("collection");
    const sourceParam = searchParams.get("source") as "insights" | "navigation" | null;

    // Only update if the collection actually changed
    if (collectionParam !== selectedCollection) {
      if (collectionParam) {
        selectCollection(collectionParam, sourceParam || "navigation");
      } else {
        selectCollection(null, sourceParam || "navigation");
      }
    }
  }, [searchParams.get("collection"), selectedCollection, selectCollection]);

  // Update URL when selected collection changes (but avoid infinite loops)
  useEffect(() => {
    const currentCollection = searchParams.get("collection");
    const currentSource = searchParams.get("source");

    // Only update URL if there's actually a difference
    const shouldUpdateCollection = selectedCollection !== currentCollection;
    const shouldUpdateSource = collectionSource && collectionSource !== currentSource;

    if (shouldUpdateCollection || shouldUpdateSource) {
      const params = new URLSearchParams();

      if (selectedCollection) {
        params.set("collection", selectedCollection);
      }

      if (collectionSource) {
        params.set("source", collectionSource);
      }

      // Use replace to avoid adding to history stack
      setSearchParams(params, { replace: true });
    }
  }, [selectedCollection, collectionSource]);

  // Filter and sort games based on current filters, selected collection, and sort order
  const filteredAndSortedGames = useMemo(() => {
    let games = library;

    // Filter by selected collection (regular or smart)
    if (selectedCollection) {
      const collection = collections.find((c) => c.id === selectedCollection);
      if (collection) {
        // Regular collection
        games = games.filter((game) => collection.gameIds.includes(game.id));
      } else {
        // Smart collection
        switch (selectedCollection) {
          case "recently-played":
            games = games.filter(game => wasPlayedRecently(game.lastTimePlayed));
            break;
          case "favorites":
            games = games.filter(game => game.favorite);
            break;
          case "installed":
            games = games.filter(game => Boolean(game.executablePath));
            break;
          case "not-played":
            games = games.filter(game => !game.playTimeInMilliseconds || game.playTimeInMilliseconds === 0);
            break;
          default:
            // Unknown collection, show all games
            break;
        }
      }
    }

    // Filter by search query
    if (filters.searchQuery && typeof filters.searchQuery === 'string') {
      const query = filters.searchQuery.toLowerCase();
      games = games.filter((game) =>
        game.title.toLowerCase().includes(query)
      );
    }

    // Filter by installed only
    if (filters.showInstalledOnly) {
      games = games.filter((game) => Boolean(game.executablePath));
    }

    // Filter by not installed only
    if (filters.showNotInstalledOnly) {
      games = games.filter((game) => !Boolean(game.executablePath));
    }

    // Filter by genres - use improved multi-genre system
    if (filters.genres.length > 0) {
      games = filterGamesByGenres(games, filters.genres);
    }

    // Apply sorting
    games = sortLibraryGames(games, filters.sortBy);

    return games;
  }, [library, filters, selectedCollection, collections]);

  const handleViewModeChange = useCallback(
    (mode: "grid" | "list") => {
      changeViewMode(mode);
    },
    [changeViewMode]
  );

  const handleSearchChange = useCallback(
    (value: string) => {
      updateFilters({ searchQuery: value });
    },
    [updateFilters]
  );

  const handleClearFilters = useCallback(() => {
    resetFilters();
    selectCollection(null);
    // Clear URL parameters when clearing filters
    setSearchParams({});
  }, [resetFilters, selectCollection, setSearchParams]);

  const handleSortChange = useCallback(
    (sortBy: typeof filters.sortBy) => {
      updateSortBy(sortBy);
    },
    [updateSortBy]
  );

  const handleCreateCollection = useCallback(() => {
    setShowCollectionModal(true);
  }, []);

  const handleCloseCollectionModal = useCallback(() => {
    setShowCollectionModal(false);
  }, []);

  const handleClearSearch = useCallback(() => {
    updateFilters({ searchQuery: "" });
  }, [updateFilters]);



  const toggleCollectionsSidebar = useCallback(() => {
    setShowCollectionsSidebar(!showCollectionsSidebar);
  }, [showCollectionsSidebar]);

  const handleAddGameToCollection = useCallback((game: LibraryGame) => {
    setSelectedGameForCollection(game);
    setShowCollectionSelector(true);
  }, []);

  const handleRemoveGameFromCollection = useCallback(async (game: LibraryGame) => {
    if (!selectedCollection) return;

    try {
      await removeGameFromCollectionById(selectedCollection, game.id);
    } catch (error) {
      console.error("Failed to remove game from collection:", error);
    }
  }, [selectedCollection, removeGameFromCollectionById]);

  const handleCloseCollectionSelector = useCallback(() => {
    setShowCollectionSelector(false);
    setSelectedGameForCollection(null);
  }, []);

  const getEmptyStateType = () => {
    if (library.length === 0) return "empty-library";
    if (filters.searchQuery && typeof filters.searchQuery === 'string' && filteredAndSortedGames.length === 0) return "no-search-results";
    if (selectedCollection && filteredAndSortedGames.length === 0) return "empty-collection";
    return "empty-library";
  };

  const getSelectedCollectionName = () => {
    if (!selectedCollection) return "";
    const collection = collections.find((c) => c.id === selectedCollection);
    return collection?.name || "";
  };

  const getCollectionGameCount = (collectionId: string) => {
    const collection = collections.find((c) => c.id === collectionId);
    if (!collection) return 0;
    return collection.gameIds.length;
  };

  const [showEditCollectionModal, setShowEditCollectionModal] = useState(false);
  const [showDeleteCollectionModal, setShowDeleteCollectionModal] = useState(false);
  const [editingCollection, setEditingCollection] = useState<GameCollection | null>(null);

  const handleEditCollection = useCallback((collection: GameCollection) => {
    setEditingCollection(collection);
    setShowEditCollectionModal(true);
  }, []);

  const handleDeleteCollection = useCallback((collection: GameCollection) => {
    setEditingCollection(collection);
    setShowDeleteCollectionModal(true);
  }, []);

  const handleCloseEditModal = useCallback(() => {
    setShowEditCollectionModal(false);
    setEditingCollection(null);
  }, []);

  const handleCloseDeleteModal = useCallback(() => {
    setShowDeleteCollectionModal(false);
    setEditingCollection(null);
  }, []);

  useEffect(() => {
    const initializeLibrary = async () => {
      setIsLoading(true);
      setError(null);
      try {
        await loadCollections();
      } catch (err) {
        setError(t("failed_to_load_collections"));
        console.error("Failed to load collections:", err);
      } finally {
        setIsLoading(false);
      }
    };

    initializeLibrary();
  }, [loadCollections]);

  if (isLoading) {
    return <LibraryLoading />;
  }

  if (error) {
    return (
      <div className="library">
        <div className="library__error">
          <h2>{t("error_loading_library")}</h2>
          <p>{error}</p>
          <Button onClick={() => window.location.reload()} theme="primary">
            {t("retry")}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="library">
      {/* Enhanced Navigation Bar */}
      <LibraryNavigationBar
        games={library}
        searchQuery={filters.searchQuery}
        sortBy={filters.sortBy}
        viewMode={viewMode}
        cardSize={cardSize}
        filters={filters}
        onSearchChange={handleSearchChange}
        onSortChange={handleSortChange}
        onViewModeChange={handleViewModeChange}
        onCardSizeChange={changeCardSize}
        onToggleCollectionsSidebar={toggleCollectionsSidebar}
        onUpdateFilters={updateFilters}
        onClearFilters={handleClearFilters}
        collectionsVisible={showCollectionsSidebar}
      />

      {/* Collections Sidebar */}
      <LibraryCollectionsSidebar
        collections={collections}
        selectedCollection={selectedCollection}
        library={library}
        isVisible={showCollectionsSidebar}
        onSelectCollection={selectCollection}
        onCreateCollection={handleCreateCollection}
        onEditCollection={handleEditCollection}
        onDeleteCollection={handleDeleteCollection}
        onClose={() => setShowCollectionsSidebar(false)}
      />



      {/* Collection Header - Always show to provide context */}
      <LibraryCollectionHeader />

      {/* What's New Section - Only render when we have games */}
      {library.length > 0 && <LibraryNews />}

      {/* Games Content */}
      <section className="library__content">
        {isLoadingGames ? (
          <LibrarySkeleton viewMode={viewMode} />
        ) : filteredAndSortedGames.length === 0 ? (
          <LibraryEmptyState
            type={getEmptyStateType()}
            searchQuery={filters.searchQuery}
            collectionName={getSelectedCollectionName()}
            onClearSearch={handleClearSearch}
          />
        ) : viewMode === "grid" ? (
          <LibraryGameGrid
            games={filteredAndSortedGames}
            cardSize={cardSize}
            onAddToCollection={handleAddGameToCollection}
            onRemoveFromCollection={handleRemoveGameFromCollection}
          />
        ) : (
          <LibraryGameList
            games={filteredAndSortedGames}
            onAddToCollection={handleAddGameToCollection}
            onRemoveFromCollection={handleRemoveGameFromCollection}
          />
        )}
      </section>

      {/* Modals */}
      {showCollectionModal && (
        <CollectionModal
          onClose={handleCloseCollectionModal}
          onSave={handleCloseCollectionModal}
        />
      )}

      {showCollectionSelector && selectedGameForCollection && (
        <CollectionSelectorModal
          game={selectedGameForCollection}
          onClose={handleCloseCollectionSelector}
        />
      )}

      {/* Edit Collection Modal */}
      {showEditCollectionModal && editingCollection && (
        <CollectionModal
          collection={editingCollection}
          onClose={handleCloseEditModal}
          onSave={handleCloseEditModal}
        />
      )}

      {/* Delete Collection Modal */}
      {showDeleteCollectionModal && editingCollection && (
        <DeleteCollectionModal
          collection={editingCollection}
          onClose={handleCloseDeleteModal}
          onDeleted={handleCloseDeleteModal}
        />
      )}
    </div>
  );
}
