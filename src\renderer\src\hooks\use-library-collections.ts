import { useCallback, useState, useEffect, useMemo } from "react";
import { useAppDispatch, useAppSelector } from "./redux";
import {
  setCollections,
  addCollection,
  updateCollection,
  removeCollection,
  addGameToCollection,
  removeGameFromCollection,
  setViewMode,
  setCardSize,
  setLibraryFilters,
  clearLibraryFilters,
  setSelectedCollection,
  setCollectionSource,
} from "@renderer/features/library-collections-slice";

// Import optimized selectors
import {
  selectCollections,
  selectCollectionsMap,
  selectCollectionGameSets,
  selectCollectionStats,
  selectSmartCollections,
  selectLibraryFilters,
  selectSelectedCollection,
  selectViewMode,
  selectCardSize,
} from "@renderer/features/library-selectors";

import type { GameCollection, LibraryViewMode, LibraryCardSize, LibraryFilters, LibraryGame, LibrarySortBy } from "@types";

interface CreateCollectionData {
  name: string;
  description?: string;
  color: string;
}

interface UpdateCollectionData {
  name?: string;
  description?: string;
  color?: string;
}

export function useLibraryCollections() {
  const dispatch = useAppDispatch();

  // Use optimized selectors for better performance
  const collections = useAppSelector(selectCollections);
  const viewMode = useAppSelector(selectViewMode);
  const cardSize = useAppSelector(selectCardSize);
  const filters = useAppSelector(selectLibraryFilters);
  const selectedCollection = useAppSelector(selectSelectedCollection);
  const collectionSource = useAppSelector((state) => state.libraryCollections.collectionSource);

  // Use memoized selectors for expensive computations
  const collectionsMap = useAppSelector(selectCollectionsMap);
  const collectionGameSets = useAppSelector(selectCollectionGameSets);
  const collectionStats = useAppSelector(selectCollectionStats);
  const smartCollections = useAppSelector(selectSmartCollections);

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load collections on mount
  useEffect(() => {
    loadCollections();
  }, []);

  const createCollection = useCallback(
    async (data: CreateCollectionData) => {
      try {
        setIsLoading(true);
        setError(null);

        const newCollection: GameCollection = {
          id: crypto.randomUUID(),
          name: data.name,
          description: data.description,
          color: data.color,
          gameIds: [],
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        // Save to database first
        await window.electron.saveCollection(newCollection);

        // Then update Redux state
        dispatch(addCollection(newCollection));

        return newCollection;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to create collection";
        setError(errorMessage);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [dispatch]
  );

  const editCollection = useCallback(
    async (collectionId: string, data: UpdateCollectionData) => {
      try {
        setIsLoading(true);
        setError(null);

        const existingCollection = collections.find((c) => c.id === collectionId);
        if (!existingCollection) {
          throw new Error("Collection not found");
        }

        const updatedCollection: GameCollection = {
          ...existingCollection,
          ...data,
          updatedAt: new Date(),
        };

        // Update database first
        await window.electron.updateCollection(updatedCollection);

        // Then update Redux state
        dispatch(updateCollection(updatedCollection));

        return updatedCollection;
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to update collection";
        setError(errorMessage);
        throw err;
      } finally {
        setIsLoading(false);
      }
    },
    [dispatch, collections]
  );

  const deleteCollection = useCallback(
    async (collectionId: string) => {
      // Delete from database first
      await window.electron.deleteCollection(collectionId);

      // Then update Redux state
      dispatch(removeCollection(collectionId));

      // If the deleted collection was selected, clear the selection
      if (selectedCollection === collectionId) {
        dispatch(setSelectedCollection(null));
      }
    },
    [dispatch, selectedCollection]
  );

  const addGameToCollectionById = useCallback(
    async (collectionId: string, gameId: string) => {
      // Update database first
      await window.electron.addGameToCollection(collectionId, gameId);

      // Then update Redux state
      dispatch(addGameToCollection({ collectionId, gameId }));
    },
    [dispatch]
  );

  const removeGameFromCollectionById = useCallback(
    async (collectionId: string, gameId: string) => {
      // Update database first
      await window.electron.removeGameFromCollection(collectionId, gameId);

      // Then update Redux state
      dispatch(removeGameFromCollection({ collectionId, gameId }));
    },
    [dispatch]
  );

  const changeViewMode = useCallback(
    (mode: LibraryViewMode) => {
      dispatch(setViewMode(mode));

      // Save preference to localStorage
      localStorage.setItem("libraryViewMode", mode);
    },
    [dispatch]
  );

  const changeCardSize = useCallback(
    (size: LibraryCardSize) => {
      dispatch(setCardSize(size));

      // Save preference to localStorage
      localStorage.setItem("libraryCardSize", size);
    },
    [dispatch]
  );

  const updateFilters = useCallback(
    (newFilters: Partial<LibraryFilters>) => {
      dispatch(setLibraryFilters(newFilters));
    },
    [dispatch]
  );

  const updateSortBy = useCallback(
    (sortBy: LibrarySortBy) => {
      dispatch(setLibraryFilters({ sortBy }));

      // Save preference to localStorage
      localStorage.setItem("librarySortBy", sortBy);
    },
    [dispatch]
  );

  const resetFilters = useCallback(() => {
    dispatch(clearLibraryFilters());
  }, [dispatch]);

  const selectCollection = useCallback(
    (collectionId: string | null, source: "insights" | "navigation" = "navigation") => {
      // Only update if there's actually a change to prevent infinite loops
      if (collectionId !== selectedCollection || source !== collectionSource) {
        dispatch(setSelectedCollection(collectionId));
        dispatch(setCollectionSource(source));
      }
    },
    [dispatch, selectedCollection, collectionSource]
  );

  const loadCollections = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Load collections from database
      const savedCollections = await window.electron.getCollections();
      dispatch(setCollections(savedCollections));
    } catch (error) {
      console.error("Failed to load collections:", error);

      // Fallback to localStorage for backward compatibility
      try {
        const savedCollections = localStorage.getItem("libraryCollections");
        if (savedCollections) {
          const parsedCollections = JSON.parse(savedCollections);
          dispatch(setCollections(parsedCollections));
        }
      } catch (fallbackError) {
        console.error("Failed to load collections from localStorage:", fallbackError);
        setError("Failed to load collections");
      }
    } finally {
      setIsLoading(false);
    }

    // Load view mode preference
    const savedViewMode = localStorage.getItem("libraryViewMode") as LibraryViewMode;
    if (savedViewMode && (savedViewMode === "grid" || savedViewMode === "list")) {
      dispatch(setViewMode(savedViewMode));
    }

    // Load card size preference
    const savedCardSize = localStorage.getItem("libraryCardSize") as LibraryCardSize;
    if (savedCardSize && (savedCardSize === "compact" || savedCardSize === "normal" || savedCardSize === "large")) {
      dispatch(setCardSize(savedCardSize));
    }
  }, [dispatch]);

  const saveCollections = useCallback(() => {
    // Save to localStorage as a fallback for backward compatibility
    try {
      localStorage.setItem("libraryCollections", JSON.stringify(collections));
    } catch (error) {
      console.error("Failed to save collections to localStorage:", error);
    }
  }, [collections]);

  // Optimized getters using memoized map for O(1) lookups
  const getCollectionById = useCallback(
    (collectionId: string) => {
      return collectionsMap.get(collectionId) || null;
    },
    [collectionsMap]
  );

  const getCollectionsForGame = useCallback(
    (gameId: string) => {
      return collections.filter((collection) =>
        collection.gameIds.includes(gameId)
      );
    },
    [collections]
  );



  const getGamesInCollection = useCallback(
    (collectionId: string, allGames: LibraryGame[]) => {
      const gameSet = collectionGameSets.get(collectionId);
      if (!gameSet) return [];

      return allGames.filter(game => gameSet.has(game.id));
    },
    [collectionGameSets]
  );

  const isGameInCollection = useCallback(
    (gameId: string, collectionId: string) => {
      const gameSet = collectionGameSets.get(collectionId);
      return gameSet ? gameSet.has(gameId) : false;
    },
    [collectionGameSets]
  );

  // Memoized collection stats to avoid recalculation
  const getCollectionStats = useCallback(
    (collectionId: string, allGames: LibraryGame[]) => {
      const gamesInCollection = getGamesInCollection(collectionId, allGames);

      let totalPlayTime = 0;
      let lastPlayed: Date | null = null;

      for (const game of gamesInCollection) {
        totalPlayTime += game.playTimeInMilliseconds || 0;

        if (game.lastTimePlayed) {
          const gameDate = new Date(game.lastTimePlayed);
          if (!lastPlayed || gameDate > lastPlayed) {
            lastPlayed = gameDate;
          }
        }
      }

      return {
        gameCount: gamesInCollection.length,
        totalPlayTime,
        lastPlayed,
      };
    },
    [getGamesInCollection]
  );



  return {
    // State
    collections,
    viewMode,
    cardSize,
    filters,
    selectedCollection,
    collectionSource,
    isLoading,
    error,

    // Actions
    createCollection,
    editCollection,
    deleteCollection,
    addGameToCollectionById,
    removeGameFromCollectionById,
    changeViewMode,
    changeCardSize,
    updateFilters,
    updateSortBy,
    resetFilters,
    selectCollection,
    loadCollections,
    saveCollections,

    // Optimized getters using memoized selectors
    getCollectionById,
    getCollectionsForGame,
    getGamesInCollection,
    isGameInCollection,
    getCollectionStats,

    // Memoized data for direct access
    collectionsMap,
    collectionGameSets,
    collectionStats,
    smartCollections,
  };
}
