@use "../../../scss/globals.scss";

.library-collection-header {
  margin-bottom: calc(globals.$spacing-unit * 4);
  position: relative;
  animation: headerSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  // Always make the header prominent and visible
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.03) 100%);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 24px;
  backdrop-filter: blur(24px);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);

  // Enhanced styling when accessed from insights
  &--from-insights {
    margin-bottom: calc(globals.$spacing-unit * 4.5);

    // Add special glow effect for insights
    box-shadow:
      0 12px 48px rgba(0, 0, 0, 0.3),
      0 6px 24px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.2),
      0 0 0 1px rgba(255, 255, 255, 0.1);

    .library-collection-header__content {
      transform: translateY(-2px);
      box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.4),
        0 8px 32px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    }

    .library-collection-header__insights-glow {
      opacity: 1;
      animation: insightsGlowPulse 3s ease-in-out infinite;
    }
  }

  // Styling for the default "All Games" view - Make it always prominent
  &--all-games {
    margin-bottom: calc(globals.$spacing-unit * 4.5);

    // Enhanced visual prominence for the default view
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.15) 0%, rgba(99, 102, 241, 0.08) 100%);
    border-color: rgba(99, 102, 241, 0.25);

    .library-collection-header__content {
      background: linear-gradient(135deg, rgba(99, 102, 241, 0.12) 0%, rgba(99, 102, 241, 0.06) 100%);
      border: 1px solid rgba(99, 102, 241, 0.2);
      box-shadow:
        0 12px 48px rgba(99, 102, 241, 0.15),
        0 6px 24px rgba(0, 0, 0, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
      padding: calc(globals.$spacing-unit * 4) calc(globals.$spacing-unit * 5);
    }

    .library-collection-header__icon {
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
      box-shadow:
        0 12px 48px rgba(99, 102, 241, 0.4),
        0 6px 24px rgba(99, 102, 241, 0.3) !important;
      width: 72px;
      height: 72px;
    }

    .library-collection-header__title {
      color: rgba(255, 255, 255, 0.98);
      font-weight: 700;
      font-size: 32px;
    }

    .library-collection-header__description {
      color: rgba(255, 255, 255, 0.8);
      font-size: 16px;
      font-weight: 500;
    }

    .library-collection-header__gradient {
      opacity: 0.9;
    }
  }

  &__backdrop {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 20px;
    overflow: hidden;
    z-index: 0;
  }

  &__gradient {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.6;
  }

  &__insights-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120%;
    height: 120%;
    opacity: 0;
    transition: opacity 0.6s ease;
    filter: blur(40px);
  }

  &__content {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: calc(globals.$spacing-unit * 3.5) calc(globals.$spacing-unit * 4.5);
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.08) 0%,
      rgba(255, 255, 255, 0.04) 50%,
      rgba(255, 255, 255, 0.02) 100%);
    border: 1px solid rgba(255, 255, 255, 0.12);
    border-radius: 20px;
    backdrop-filter: blur(32px);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.2),
      0 4px 16px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    margin: 0 auto;
    max-width: 100%;
    width: 100%;

    // Subtle inner glow
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.05) 0%,
        transparent 40%);
      border-radius: 20px;
      pointer-events: none;
    }

    // Steam Deck optimizations
    @media (max-width: 1280px) and (max-height: 800px) {
      padding: calc(globals.$spacing-unit * 4) calc(globals.$spacing-unit * 4.5);
      gap: calc(globals.$spacing-unit * 3.5);
    }

    @media (max-width: 1024px) {
      padding: calc(globals.$spacing-unit * 3) calc(globals.$spacing-unit * 3.5);
      gap: calc(globals.$spacing-unit * 3);
    }

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: calc(globals.$spacing-unit * 2.5);
      padding: calc(globals.$spacing-unit * 2.5) calc(globals.$spacing-unit * 3);
    }

    @media (max-width: 480px) {
      padding: calc(globals.$spacing-unit * 2) calc(globals.$spacing-unit * 2.5);
      gap: calc(globals.$spacing-unit * 2);
    }

    @media (max-width: 360px) {
      padding: calc(globals.$spacing-unit * 1.5) calc(globals.$spacing-unit * 2);
      gap: calc(globals.$spacing-unit * 1.5);
    }
  }

  &__main {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.5);
    flex: 1;

    @media (max-width: 768px) {
      flex-direction: column;
      text-align: center;
      gap: calc(globals.$spacing-unit);
    }
  }

  &__icon-wrapper {
    position: relative;
    flex-shrink: 0;
  }

  &__icon {
    width: 64px;
    height: 64px;
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 28px;
    position: relative;
    z-index: 2;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 4px 16px rgba(0, 0, 0, 0.2);

    // Enhanced glow effect
    &::before {
      content: '';
      position: absolute;
      top: -3px;
      left: -3px;
      right: -3px;
      bottom: -3px;
      border-radius: 19px;
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.05));
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover::before {
      opacity: 1;
    }

    @media (max-width: 768px) {
      width: 48px;
      height: 48px;
      font-size: 20px;
    }
  }

  &__insights-ring {
    position: absolute;
    top: -6px;
    left: -6px;
    right: -6px;
    bottom: -6px;
    border: 2px solid;
    border-radius: 22px;
    opacity: 0.6;
    animation: insightsRingPulse 2s ease-in-out infinite;
  }

  &__text {
    flex: 1;
    min-width: 0;
  }

  &__title-row {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit);
    margin-bottom: calc(globals.$spacing-unit * 0.5);

    @media (max-width: 768px) {
      flex-direction: column;
      gap: calc(globals.$spacing-unit * 0.5);
      text-align: center;
    }
  }

  &__info {
    flex: 1;
    min-width: 0;
    position: relative;

    @media (max-width: 768px) {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  &__title {
    margin: 0;
    font-size: 28px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.95);
    line-height: 1.2;
    letter-spacing: -0.02em;
    text-shadow: 0 2px 12px rgba(0, 0, 0, 0.4);

    @media (max-width: 1024px) {
      font-size: 24px;
    }

    @media (max-width: 768px) {
      font-size: 20px;
    }

    @media (max-width: 480px) {
      font-size: 18px;
    }
  }

  &__insights-badge {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.375);
    font-size: 11px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    background: linear-gradient(135deg,
      rgba(99, 102, 241, 0.25) 0%,
      rgba(139, 92, 246, 0.25) 100%);
    padding: calc(globals.$spacing-unit * 0.375) calc(globals.$spacing-unit * 0.75);
    border-radius: 12px;
    border: 1px solid rgba(99, 102, 241, 0.4);
    backdrop-filter: blur(8px);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
    animation: badgeGlow 3s ease-in-out infinite;

    @media (max-width: 768px) {
      font-size: 10px;
      padding: calc(globals.$spacing-unit * 0.25) calc(globals.$spacing-unit * 0.5);
    }
  }

  &__insights-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: linear-gradient(135deg, #6366f1, #8b5cf6);
    animation: insightsDotPulse 2s ease-in-out infinite;
  }

  &__insights-text {
    // Text styling handled by parent
  }

  &__description {
    margin: calc(globals.$spacing-unit * 0.75) 0 0 0;
    font-size: 16px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.85);
    line-height: 1.5;
    opacity: 0.95;

    @media (max-width: 1024px) {
      font-size: 15px;
    }

    @media (max-width: 768px) {
      font-size: 14px;
      text-align: center;
    }

    @media (max-width: 480px) {
      font-size: 13px;
    }
  }

  &__stats {
    display: flex;
    align-items: center;
    gap: calc(globals.$spacing-unit * 1.5);
    flex-shrink: 0;

    @media (max-width: 768px) {
      justify-content: center;
      gap: calc(globals.$spacing-unit);
    }
  }

  &__stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: calc(globals.$spacing-unit * 0.25);
    padding: calc(globals.$spacing-unit * 0.75) calc(globals.$spacing-unit);
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.08) 0%,
      rgba(255, 255, 255, 0.03) 100%);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.12) 0%,
        rgba(255, 255, 255, 0.06) 100%);
      border-color: rgba(255, 255, 255, 0.15);
      transform: translateY(-1px);
    }

    &--primary {
      border-color: rgba(99, 102, 241, 0.3);
      background: linear-gradient(135deg,
        rgba(99, 102, 241, 0.15) 0%,
        rgba(99, 102, 241, 0.05) 100%);

      &:hover {
        border-color: rgba(99, 102, 241, 0.4);
        background: linear-gradient(135deg,
          rgba(99, 102, 241, 0.2) 0%,
          rgba(99, 102, 241, 0.08) 100%);
      }
    }

    &--secondary {
      // Uses default styling
    }

    @media (max-width: 768px) {
      padding: calc(globals.$spacing-unit * 0.5) calc(globals.$spacing-unit * 0.75);
    }
  }

  &__stat-number {
    font-size: 18px;
    font-weight: 700;
    color: globals.$muted-color;
    line-height: 1;

    @media (max-width: 768px) {
      font-size: 16px;
    }
  }

  &__stat-label {
    font-size: 11px;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.7);
    text-transform: uppercase;
    letter-spacing: 0.5px;

    @media (max-width: 768px) {
      font-size: 10px;
    }
  }
}

// Animation keyframes
@keyframes headerSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes insightsGlowPulse {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

@keyframes insightsRingPulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes badgeGlow {
  0%, 100% {
    box-shadow:
      0 0 12px rgba(99, 102, 241, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow:
      0 0 24px rgba(99, 102, 241, 0.5),
      0 0 8px rgba(139, 92, 246, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }
}

@keyframes insightsDotPulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}
